<?php
declare(strict_types=1);

namespace CopeX\CustomerGroupRedirect\Helper;

use Exception;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\Session\SessionManagerInterface;
use Magento\Framework\Stdlib\Cookie\CookieMetadataFactory;
use Magento\Framework\Stdlib\CookieManagerInterface;
use Magento\Store\Api\Data\StoreInterface;
use Magento\Store\Model\ScopeInterface;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

class Data extends AbstractHelper
{
    public const CONFIG_PATH_ENABLED = 'copex_customergroupredirect/general/enabled';
    public const CONFIG_PATH_PRESERVE_CART = 'copex_customergroupredirect/general/preserve_cart';
    public const CONFIG_PATH_DEBUG = 'copex_customergroupredirect/general/debug';
    public const CONFIG_PATH_REDIRECT_TO_HOMEPAGE = 'copex_customergroupredirect/general/redirect_to_homepage';
    public const CONFIG_PATH_CUSTOMER_GROUPS = 'copex_customergroupredirect/general/customer_groups';
    public const CONFIG_PATH_B2B_WEBSITE_CODE = 'copex_customergroupredirect/general/b2b_website_code';
    public const CONFIG_PATH_B2C_WEBSITE_CODE = 'copex_customergroupredirect/general/b2c_website_code';
    public const CONFIG_PATH_B2B_URL = 'copex_customergroupredirect/general/b2b_url';
    public const CONFIG_PATH_B2C_URL = 'copex_customergroupredirect/general/b2c_url';
    public const CONFIG_PATH_STORE_PAIRS = 'copex_customergroupredirect/store_mapping/store_pairs';
    public const SESSION_QUOTE_ID_KEY = 'copex_preserved_quote_id';
    public const SESSION_REDIRECT_PROCESSED = 'copex_redirect_processed';

    private StoreManagerInterface $storeManager;
    private CookieManagerInterface $cookieManager;
    private CookieMetadataFactory $cookieMetadataFactory;
    private SessionManagerInterface $sessionManager;
    private LoggerInterface $logger;

    public function __construct(
        Context $context,
        StoreManagerInterface $storeManager,
        CookieManagerInterface $cookieManager,
        CookieMetadataFactory $cookieMetadataFactory,
        SessionManagerInterface $sessionManager,
        LoggerInterface $logger
    ) {
        parent::__construct($context);
        $this->storeManager = $storeManager;
        $this->cookieManager = $cookieManager;
        $this->cookieMetadataFactory = $cookieMetadataFactory;
        $this->sessionManager = $sessionManager;
        $this->logger = $logger;
    }

    /**
     * Check if module is enabled
     */
    public function isEnabled(): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::CONFIG_PATH_ENABLED,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Check if cart preservation is enabled
     */
    public function isCartPreservationEnabled(): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::CONFIG_PATH_PRESERVE_CART,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Check if debug mode is enabled
     */
    public function isDebugEnabled(): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::CONFIG_PATH_DEBUG,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Check if should always redirect to homepage
     */
    public function shouldRedirectToHomepage(): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::CONFIG_PATH_REDIRECT_TO_HOMEPAGE,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Get target store for customer group
     */
    public function getTargetStoreForCustomerGroup(int $customerGroupId): ?StoreInterface
    {
        try {
            // Check if this customer group is configured as B2B
            $isB2bCustomer = $this->isB2bCustomerGroup($customerGroupId);
            $currentWebsite = $this->storeManager->getWebsite();
            $currentWebsiteCode = $currentWebsite->getCode();

            // Get website codes from configuration
            $b2bWebsiteCode = $this->getB2bWebsiteCode();
            $b2cWebsiteCode = $this->getB2cWebsiteCode();

            // Determine target website based on customer group and current website
            $targetWebsiteCode = null;

            if ($isB2bCustomer && $currentWebsiteCode === $b2cWebsiteCode) {
                // B2B customer on B2C website -> redirect to B2B
                $targetWebsiteCode = $b2bWebsiteCode;
            } elseif (! $isB2bCustomer && $currentWebsiteCode === $b2bWebsiteCode) {
                // B2C customer on B2B website -> redirect to B2C
                $targetWebsiteCode = $b2cWebsiteCode;
            }

            if (! $targetWebsiteCode) {
                return null; // No redirect needed
            }

            // Get target website
            $targetWebsite = $this->storeManager->getWebsite($targetWebsiteCode);

            // Get current store ID for mapping
            $currentDefaultStore = $currentWebsite->getDefaultStore();
            if (! $currentDefaultStore) {
                return null;
            }

            $currentStoreId = (int) $currentDefaultStore->getId();

            // Get target store using store mapping
            $targetStoreId = $this->getTargetStoreId($currentStoreId, $targetWebsiteCode);

            $this->logDebug('Store mapping lookup', [
                'current_store_id' => $currentStoreId,
                'target_website_code' => $targetWebsiteCode,
                'target_store_id' => $targetStoreId,
                'store_mapping' => $this->getStoreMapping(),
            ]);

            if ($targetStoreId) {
                return $this->storeManager->getStore($targetStoreId);
            }

            // Fallback to default store of target website
            return $targetWebsite->getDefaultStore();
        } catch (Exception $e) {
            $this->logger->error('Error getting target store for customer group: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Check if customer group is configured as B2B
     */
    public function isB2bCustomerGroup(int $customerGroupId): bool
    {
        $b2bGroups = $this->getB2bCustomerGroups();
        return in_array($customerGroupId, $b2bGroups);
    }

    /**
     * Get B2B customer groups from configuration
     */
    public function getB2bCustomerGroups(): array
    {
        $groups = $this->scopeConfig->getValue(
            self::CONFIG_PATH_CUSTOMER_GROUPS,
            ScopeInterface::SCOPE_STORE
        );

        if (! $groups) {
            return [];
        }

        return explode(',', $groups);
    }

    /**
     * Get B2B website code
     */
    public function getB2bWebsiteCode(): string
    {
        return (string) $this->scopeConfig->getValue(
            self::CONFIG_PATH_B2B_WEBSITE_CODE,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Get B2C website code
     */
    public function getB2cWebsiteCode(): string
    {
        return (string) $this->scopeConfig->getValue(
            self::CONFIG_PATH_B2C_WEBSITE_CODE,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Get target store ID based on store mapping
     */
    public function getTargetStoreId(int $currentStoreId, string $targetWebsiteCode): ?int
    {
        $storeMapping = $this->getStoreMapping();
        $b2bWebsiteCode = $this->getB2bWebsiteCode();

        if ($targetWebsiteCode === $b2bWebsiteCode) {
            // Redirecting to B2B - use b2c_to_b2b mapping
            return $storeMapping['b2c_to_b2b'][$currentStoreId] ?? null;
        }
        // Redirecting to B2C - use b2b_to_b2c mapping
        return $storeMapping['b2b_to_b2c'][$currentStoreId] ?? null;
    }

    /**
     * Get store mapping from configuration
     */
    public function getStoreMapping(): array
    {
        $configMapping = $this->scopeConfig->getValue(
            self::CONFIG_PATH_STORE_PAIRS,
            ScopeInterface::SCOPE_STORE
        );

        if (! $configMapping) {
            return [
                'b2c_to_b2b' => [],
                'b2b_to_b2c' => [],
            ];
        }

        $pairs = explode("\n", $configMapping);
        $b2cToB2b = [];
        $b2bToB2c = [];

        foreach ($pairs as $pair) {
            $stores = explode(':', trim($pair));
            if (count($stores) === 2) {
                $b2cStoreId = (int) trim($stores[0]);
                $b2bStoreId = (int) trim($stores[1]);
                $b2cToB2b[$b2cStoreId] = $b2bStoreId;
                $b2bToB2c[$b2bStoreId] = $b2cStoreId;
            }
        }

        return [
            'b2c_to_b2b' => $b2cToB2b,
            'b2b_to_b2c' => $b2bToB2c,
        ];
    }

    /**
     * Store quote ID to be preserved during redirection
     */
    public function setPreservedQuoteId(int $quoteId): void
    {
        $this->sessionManager->setData(self::SESSION_QUOTE_ID_KEY, $quoteId);
    }

    /**
     * Get preserved quote ID
     */
    public function getPreservedQuoteId(): ?int
    {
        return $this->sessionManager->getData(self::SESSION_QUOTE_ID_KEY);
    }

    /**
     * Clear preserved quote ID
     */
    public function clearPreservedQuoteId(): void
    {
        $this->sessionManager->unsetData(self::SESSION_QUOTE_ID_KEY);
    }

    /**
     * Get redirect URL for B2B or B2C
     */
    public function getRedirectUrl(bool $isB2b = false): string
    {
        if ($isB2b) {
            return (string) $this->scopeConfig->getValue(
                self::CONFIG_PATH_B2B_URL,
                ScopeInterface::SCOPE_STORE
            );
        }
        return (string) $this->scopeConfig->getValue(
            self::CONFIG_PATH_B2C_URL,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Store cart data for cross-website transfer
     */
    public function setPreservedCartData(string $token, array $data): void
    {
        $this->sessionManager->setData('copex_cart_transfer_' . $token, $data);
    }

    /**
     * Get preserved cart data
     */
    public function getPreservedCartData(string $token): ?array
    {
        return $this->sessionManager->getData('copex_cart_transfer_' . $token);
    }

    /**
     * Clear preserved cart data
     */
    public function clearPreservedCartData(string $token): void
    {
        $this->sessionManager->unsetData('copex_cart_transfer_' . $token);
    }

    /**
     * Store session data for cross-website transfer
     */
    public function setPreservedSessionData(string $token, array $data): void
    {
        $this->sessionManager->setData('copex_session_transfer_' . $token, $data);
    }

    /**
     * Get preserved session data
     */
    public function getPreservedSessionData(string $token): ?array
    {
        return $this->sessionManager->getData('copex_session_transfer_' . $token);
    }

    /**
     * Clear preserved session data
     */
    public function clearPreservedSessionData(string $token): void
    {
        $this->sessionManager->unsetData('copex_session_transfer_' . $token);
    }

    /**
     * Log debug information
     */
    public function logDebug(string $message, array $context = []): void
    {
        if ($this->isDebugEnabled()) {
            $this->logger->debug('[CopeX_CustomerGroupRedirect] ' . $message, $context);
        }
    }
}
