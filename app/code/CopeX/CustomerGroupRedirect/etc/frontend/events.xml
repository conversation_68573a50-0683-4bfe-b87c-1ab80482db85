<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <!-- Only trigger on customer login to avoid redirect loops -->
    <event name="customer_login">
        <observer name="copex_customer_group_redirect_login"
                  instance="CopeX\CustomerGroupRedirect\Observer\CustomerGroupRedirect"/>
    </event>

    <!-- Session restoration for cross-website transfers -->
    <event name="controller_action_predispatch">
        <observer name="copex_customer_group_redirect_session_restore"
                  instance="CopeX\CustomerGroupRedirect\Observer\SessionRestoreObserver"/>
    </event>
</config>
