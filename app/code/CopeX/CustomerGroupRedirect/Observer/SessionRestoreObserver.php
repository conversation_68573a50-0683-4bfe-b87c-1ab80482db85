<?php
declare(strict_types=1);

namespace CopeX\CustomerGroupRedirect\Observer;

use CopeX\CustomerGroupRedirect\Helper\Data;
use Exception;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Checkout\Model\Session as CheckoutSession;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Quote\Model\QuoteFactory;
use Magento\Quote\Model\QuoteRepository;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

class SessionRestoreObserver implements ObserverInterface
{
    private CustomerSession $customerSession;
    private CheckoutSession $checkoutSession;
    private Data $helper;
    private RequestInterface $request;
    private QuoteFactory $quoteFactory;
    private QuoteRepository $quoteRepository;
    private StoreManagerInterface $storeManager;
    private LoggerInterface $logger;
    private ProductRepositoryInterface $productRepository;

    public function __construct(
        CustomerSession $customerSession,
        CheckoutSession $checkoutSession,
        Data $helper,
        RequestInterface $request,
        QuoteFactory $quoteFactory,
        QuoteRepository $quoteRepository,
        StoreManagerInterface $storeManager,
        LoggerInterface $logger,
        ProductRepositoryInterface $productRepository
    ) {
        $this->customerSession = $customerSession;
        $this->checkoutSession = $checkoutSession;
        $this->helper = $helper;
        $this->request = $request;
        $this->quoteFactory = $quoteFactory;
        $this->quoteRepository = $quoteRepository;
        $this->storeManager = $storeManager;
        $this->logger = $logger;
        $this->productRepository = $productRepository;
    }

    /**
     * Restore session and cart data after cross-website redirect
     */
    public function execute(Observer $observer): void
    {
        try {
            // Only process if module is enabled
            if (! $this->helper->isEnabled()) {
                return;
            }

            // Don't process AJAX requests
            if ($this->request->isXmlHttpRequest()) {
                return;
            }

            // Don't process admin or API requests
            $requestUri = $this->request->getRequestUri();
            if (str_starts_with($requestUri, '/admin') ||
                str_starts_with($requestUri, '/rest/') ||
                str_starts_with($requestUri, '/soap/')) {
                return;
            }

            // Check if we have a session token in the request
            $sessionToken = $this->request->getParam('copex_session_token');
            if (! $sessionToken) {
                return;
            }

            $this->helper->logDebug('Processing session restoration', [
                'session_token' => substr($sessionToken, 0, 8) . '...',
            ]);

            // Restore customer session if needed
            $this->restoreCustomerSession($sessionToken);

            // Restore cart if enabled
            if ($this->helper->isCartPreservationEnabled()) {
                $this->restoreCart($sessionToken);
            }
        } catch (Exception $e) {
            $this->logger->error('Error in session restore: ' . $e->getMessage());
        }
    }

    /**
     * Restore customer session data
     */
    private function restoreCustomerSession(string $sessionToken): void
    {
        try {
            $sessionData = $this->helper->getPreservedSessionData($sessionToken);

            if (! $sessionData || ! isset($sessionData['customer_id'])) {
                return;
            }

            // Check if data has expired
            if (time() > $sessionData['expires_at']) {
                $this->helper->clearPreservedSessionData($sessionToken);
                $this->helper->logDebug('Session data expired, cleared');
                return;
            }

            // If customer is not logged in, attempt to restore session
            if (! $this->customerSession->isLoggedIn()) {
                $this->customerSession->setCustomerId($sessionData['customer_id']);
                $this->customerSession->setCustomerGroupId($sessionData['customer_group_id']);

                $this->helper->logDebug('Restored customer session', [
                    'customer_id' => $sessionData['customer_id'],
                    'customer_group_id' => $sessionData['customer_group_id'],
                ]);
            }

            // Clean up the preserved data
            $this->helper->clearPreservedSessionData($sessionToken);
        } catch (Exception $e) {
            $this->helper->logDebug('Failed to restore customer session: ' . $e->getMessage());
        }
    }

    /**
     * Restore cart items
     */
    private function restoreCart(string $sessionToken): void
    {
        try {
            $cartData = $this->helper->getPreservedCartData($sessionToken);

            if (! $cartData || ! isset($cartData['items'])) {
                return;
            }

            // Check if data has expired
            if (time() > $cartData['expires_at']) {
                $this->helper->clearPreservedCartData($sessionToken);
                $this->helper->logDebug('Cart data expired, cleared');
                return;
            }

            // Check if we're on the correct target store
            $currentStoreId = $this->storeManager->getStore()->getId();
            if ($currentStoreId !== $cartData['target_store_id']) {
                $this->helper->logDebug('Not on target store, skipping cart restoration', [
                    'current_store_id' => $currentStoreId,
                    'target_store_id' => $cartData['target_store_id'],
                ]);
                return;
            }

            // Get or create quote for current session
            $quote = $this->checkoutSession->getQuote();
            if (! $quote->getId()) {
                $quote = $this->quoteFactory->create();
                $quote->setStoreId($currentStoreId);
                if ($this->customerSession->isLoggedIn()) {
                    $quote->setCustomerId($this->customerSession->getCustomerId());
                }
            }

            // Add items to quote
            $itemsAdded = 0;
            foreach ($cartData['items'] as $itemData) {
                try {
                    // Basic validation
                    if (! isset($itemData['product_id']) || ! isset($itemData['qty'])) {
                        continue;
                    }

                    // Load the product object
                    try {
                        $product = $this->productRepository->getById($itemData['product_id']);
                    } catch (NoSuchEntityException $e) {
                        $this->helper->logDebug('Product not found, skipping', [
                            'product_id' => $itemData['product_id'],
                        ]);
                        continue;
                    }

                    // Add item to quote with product object
                    $quote->addProduct($product, $itemData['qty']);
                    $itemsAdded++;
                } catch (Exception $e) {
                    $this->helper->logDebug('Failed to add item to cart', [
                        'product_id' => $itemData['product_id'] ?? 'unknown',
                        'error' => $e->getMessage(),
                    ]);
                }
            }

            if ($itemsAdded > 0) {
                $quote->collectTotals();
                $this->quoteRepository->save($quote);
                $this->checkoutSession->setQuoteId($quote->getId());

                $this->helper->logDebug('Restored cart items', [
                    'items_added' => $itemsAdded,
                    'quote_id' => $quote->getId(),
                ]);
            }

            // Clean up the preserved data
            $this->helper->clearPreservedCartData($sessionToken);
        } catch (Exception $e) {
            $this->helper->logDebug('Failed to restore cart: ' . $e->getMessage());
        }
    }
}
