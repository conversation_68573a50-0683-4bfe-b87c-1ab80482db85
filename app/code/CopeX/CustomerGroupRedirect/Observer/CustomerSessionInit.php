<?php
/** @noinspection BusinessLogicInObserverInspection */
declare(strict_types=1);

namespace CopeX\CustomerGroupRedirect\Observer;

use CopeX\CustomerGroupRedirect\Helper\Data;
use Exception;
use JetBrains\PhpStorm\NoReturn;
use Magento\Checkout\Model\Session as CheckoutSession;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Framework\App\Response\RedirectInterface;
use Magento\Framework\App\ResponseFactory;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Store\Model\ScopeInterface;
use Magento\Store\Model\StoreManagerInterface;

/**
 * @property $scopeConfig
 */
class CustomerSessionInit implements ObserverInterface
{
    private Data $helper;

    private CustomerSession $customerSession;

    private StoreManagerInterface $storeManager;

    private ResponseFactory $responseFactory;

    private RedirectInterface $redirect;

    private CheckoutSession $checkoutSession;

    private static bool $isProcessed = false;

    public function __construct(
        Data $helper,
        CustomerSession $customerSession,
        StoreManagerInterface $storeManager,
        ResponseFactory $responseFactory,
        RedirectInterface $redirect,
        CheckoutSession $checkoutSession
    ) {
        $this->helper = $helper;
        $this->customerSession = $customerSession;
        $this->storeManager = $storeManager;
        $this->responseFactory = $responseFactory;
        $this->redirect = $redirect;
        $this->checkoutSession = $checkoutSession;
    }

    /**
     * Execute observer
     *
     * @throws NoSuchEntityException
     * @throws LocalizedException
     * @throws LocalizedException
     * @throws LocalizedException
     */
    public function execute(Observer $observer): void
    {
        // Prevent multiple redirects
        if (self::$isProcessed) {
            return;
        }

        self::$isProcessed = true;

        $websiteCode = $this->storeManager->getWebsite()->getCode();
        $isB2bCustomer = $this->isB2bCustomer();

        // B2C customer on B2B website
        if ($websiteCode === $this->helper->getB2bWebsiteCode() && ! $isB2bCustomer) {
            $redirectUrl = $this->helper->getRedirectUrl();
            $this->performRedirect($redirectUrl);
        }

        // B2B customer on B2C website
        if ($websiteCode === $this->helper->getB2cWebsiteCode() && $isB2bCustomer) {
            $this->transferQuote();
            $redirectUrl = $this->helper->getRedirectUrl(true);
            $this->performRedirect($redirectUrl);
        }
    }

    /**
     * Check if current customer is a B2B customer
     */
    private function isB2bCustomer(): bool
    {
        try {
            $groupId = $this->customerSession->getCustomerGroupId();
        } catch (NoSuchEntityException|LocalizedException $e) {
        }
        $allowedGroups = $this->helper->getB2bCustomerGroups();

        return in_array($groupId, $allowedGroups);
    }

    /**
     * Get store mapping from configuration
     */
    private function getStoreMapping(): array
    {
        $configMapping = $this->scopeConfig->getValue(
            'copex_customergroupredirect/store_mapping/store_pairs',
            ScopeInterface::SCOPE_STORE
        );

        if (! $configMapping) {
            return [
                'b2c_to_b2b' => [1 => 3, 2 => 4],
                'b2b_to_b2c' => [3 => 1, 4 => 2],
            ];
        }

        $pairs = explode("\n", $configMapping);
        $b2cToB2b = [];
        $b2bToB2c = [];

        foreach ($pairs as $pair) {
            $stores = explode(':', trim($pair));
            if (count($stores) === 2) {
                $b2cToB2b[$stores[0]] = $stores[1];
                $b2bToB2c[$stores[1]] = $stores[0];
            }
        }

        return [
            'b2c_to_b2b' => $b2cToB2b,
            'b2b_to_b2c' => $b2bToB2c,
        ];
    }

    /**
     * Transfer quote items to appropriate store
     *
     * @throws NoSuchEntityException
     * @throws LocalizedException
     * @throws Exception
     * @throws LocalizedException
     */
    private function transferQuote(): void
    {
        $quote = $this->checkoutSession->getQuote();

        if (! $quote->hasItems()) {
            return;
        }

        // Get store mappings from configuration
        $storeMapping = $this->getStoreMapping();
        $b2cToB2b = $storeMapping['b2c_to_b2b'];
        $b2cStoreIds = array_keys($b2cToB2b);
        $b2bStoreIds = array_values($b2cToB2b);

        foreach ($quote->getAllItems() as $item) {
            $storeId = $item->getStoreId();

            if (in_array($storeId, $b2cStoreIds)) {
                $newStoreId = $b2cToB2b[$storeId];
                $item->setStoreId($newStoreId);
                $item->save();
            }
        }

        $storeId = $quote->getStoreId();
        if (in_array($storeId, $b2cStoreIds)) {
            $newStoreId = $b2cToB2b[$storeId];
            $quote->setStoreId($newStoreId);
            $quote->save();
        }
    }

    /**
     * Perform redirect
     */
    #[NoReturn] private function performRedirect(string $url): void
    {
        $response = $this->responseFactory->create();
        $response->setRedirect($url);
        $response->sendResponse();
        exit;
    }
}
